using CloudinaryDotNet;
using CloudinaryDotNet.Actions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Interfaces;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using System.Text.Json;

namespace NafaPlace.Catalog.Infrastructure.Services;

public interface IEnhancedCloudinaryService
{
    Task<ImageUploadResult> UploadImageAsync(Stream imageStream, string fileName, string folder = "products", ImageUploadOptions? options = null);
    Task<ImageUploadResult> UploadImageAsync(string base64Image, string fileName, string folder = "products", ImageUploadOptions? options = null);
    Task<List<ImageUploadResult>> UploadMultipleImagesAsync(List<ImageUploadData> images, string folder = "products");
    Task<bool> DeleteImageAsync(string publicId);
    Task<List<bool>> DeleteMultipleImagesAsync(List<string> publicIds);
    Task<string> GenerateOptimizedUrlAsync(string publicId, ImageTransformation? transformation = null);
    Task<ImageValidationResult> ValidateImageAsync(Stream imageStream, string fileName);
    Task<Stream> OptimizeImageAsync(Stream imageStream, ImageOptimizationOptions? options = null);
}

public class EnhancedCloudinaryService : IEnhancedCloudinaryService
{
    private readonly Cloudinary _cloudinary;
    private readonly ILogger<EnhancedCloudinaryService> _logger;
    private readonly CloudinarySettings _settings;

    public EnhancedCloudinaryService(
        IConfiguration configuration,
        ILogger<EnhancedCloudinaryService> logger)
    {
        _logger = logger;
        var cloudinarySection = configuration.GetSection("Cloudinary");
        _settings = new CloudinarySettings
        {
            CloudName = cloudinarySection["CloudName"] ?? "",
            ApiKey = cloudinarySection["ApiKey"] ?? "",
            ApiSecret = cloudinarySection["ApiSecret"] ?? ""
        };

        if (string.IsNullOrEmpty(_settings.CloudName) || 
            string.IsNullOrEmpty(_settings.ApiKey) || 
            string.IsNullOrEmpty(_settings.ApiSecret))
        {
            throw new ArgumentException("Configuration Cloudinary manquante. Vérifiez CloudName, ApiKey et ApiSecret.");
        }

        var account = new Account(_settings.CloudName, _settings.ApiKey, _settings.ApiSecret);
        _cloudinary = new Cloudinary(account);
    }

    public async Task<ImageUploadResult> UploadImageAsync(Stream imageStream, string fileName, string folder = "products", ImageUploadOptions? options = null)
    {
        try
        {
            // Valider l'image
            var validation = await ValidateImageAsync(imageStream, fileName);
            if (!validation.IsValid)
            {
                return new ImageUploadResult
                {
                    Success = false,
                    ErrorMessage = validation.ErrorMessage
                };
            }

            // Optimiser l'image si nécessaire
            var optimizationOptions = options?.OptimizationOptions ?? new ImageOptimizationOptions();
            using var optimizedStream = await OptimizeImageAsync(imageStream, optimizationOptions);

            var publicId = GeneratePublicId(fileName, folder);
            var uploadParams = new CloudinaryDotNet.Actions.ImageUploadParams()
            {
                File = new FileDescription(fileName, optimizedStream),
                PublicId = publicId,
                Folder = folder,
                Overwrite = options?.Overwrite ?? true,
                Format = options?.Format ?? "jpg",
                Tags = string.Join(",", options?.Tags ?? new[] { "nafaplace", folder ?? "default" })
            };

            // Ajouter les transformations si spécifiées
            if (options?.Transformation != null)
            {
                uploadParams.Transformation = CreateCloudinaryTransformation(options.Transformation);
            }

            var uploadResult = await _cloudinary.UploadAsync(uploadParams);

            if (uploadResult.Error != null)
            {
                _logger.LogError("Erreur lors de l'upload Cloudinary: {Error}", uploadResult.Error.Message);
                return new ImageUploadResult
                {
                    Success = false,
                    ErrorMessage = uploadResult.Error.Message
                };
            }

            _logger.LogInformation("Image uploadée avec succès: {PublicId} -> {Url}", publicId, uploadResult.SecureUrl);

            return new ImageUploadResult
            {
                Success = true,
                PublicId = uploadResult.PublicId,
                Url = uploadResult.SecureUrl.ToString(),
                Width = uploadResult.Width,
                Height = uploadResult.Height,
                Format = uploadResult.Format,
                Bytes = uploadResult.Bytes
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'upload de l'image {FileName}", fileName);
            return new ImageUploadResult
            {
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<ImageUploadResult> UploadImageAsync(string base64Image, string fileName, string folder = "products", ImageUploadOptions? options = null)
    {
        try
        {
            // Nettoyer le base64 si nécessaire
            var cleanBase64 = base64Image.Contains(",") ? base64Image.Split(',')[1] : base64Image;
            var imageBytes = Convert.FromBase64String(cleanBase64);
            
            using var imageStream = new MemoryStream(imageBytes);
            return await UploadImageAsync(imageStream, fileName, folder, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la conversion base64 pour {FileName}", fileName);
            return new ImageUploadResult
            {
                Success = false,
                ErrorMessage = $"Erreur de conversion base64: {ex.Message}"
            };
        }
    }

    public async Task<List<ImageUploadResult>> UploadMultipleImagesAsync(List<ImageUploadData> images, string folder = "products")
    {
        var results = new List<ImageUploadResult>();
        var semaphore = new SemaphoreSlim(_settings.MaxConcurrentUploads, _settings.MaxConcurrentUploads);

        var tasks = images.Select(async image =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await UploadImageAsync(image.ImageStream, image.FileName, folder, image.Options);
            }
            finally
            {
                semaphore.Release();
            }
        });

        results.AddRange(await Task.WhenAll(tasks));
        return results;
    }

    public async Task<bool> DeleteImageAsync(string publicId)
    {
        try
        {
            var deleteParams = new DeletionParams(publicId);
            var result = await _cloudinary.DestroyAsync(deleteParams);
            
            var success = result.Result == "ok";
            if (success)
            {
                _logger.LogInformation("Image supprimée avec succès: {PublicId}", publicId);
            }
            else
            {
                _logger.LogWarning("Échec de suppression de l'image: {PublicId} - {Result}", publicId, result.Result);
            }
            
            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'image {PublicId}", publicId);
            return false;
        }
    }

    public async Task<List<bool>> DeleteMultipleImagesAsync(List<string> publicIds)
    {
        var results = new List<bool>();
        var semaphore = new SemaphoreSlim(_settings.MaxConcurrentUploads, _settings.MaxConcurrentUploads);

        var tasks = publicIds.Select(async publicId =>
        {
            await semaphore.WaitAsync();
            try
            {
                return await DeleteImageAsync(publicId);
            }
            finally
            {
                semaphore.Release();
            }
        });

        results.AddRange(await Task.WhenAll(tasks));
        return results;
    }

    public async Task<string> GenerateOptimizedUrlAsync(string publicId, ImageTransformation? transformation = null)
    {
        await Task.CompletedTask;
        
        if (transformation == null)
        {
            return _cloudinary.Api.UrlImgUp.Transform(new Transformation().Quality("auto").FetchFormat("auto")).BuildUrl(publicId);
        }

        var cloudinaryTransformation = CreateCloudinaryTransformation(transformation);
        return _cloudinary.Api.UrlImgUp.Transform(cloudinaryTransformation).BuildUrl(publicId);
    }

    public async Task<ImageValidationResult> ValidateImageAsync(Stream imageStream, string fileName)
    {
        try
        {
            // Vérifier la taille du fichier
            if (imageStream.Length > _settings.MaxFileSizeBytes)
            {
                return new ImageValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"La taille du fichier ({imageStream.Length / (1024 * 1024)} MB) dépasse la limite autorisée ({_settings.MaxFileSizeBytes / (1024 * 1024)} MB)."
                };
            }

            // Vérifier l'extension
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            if (!_settings.AllowedExtensions.Contains(extension))
            {
                return new ImageValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Format de fichier non supporté: {extension}. Formats autorisés: {string.Join(", ", _settings.AllowedExtensions)}"
                };
            }

            // Vérifier que c'est bien une image valide
            imageStream.Position = 0;
            using var image = await Image.LoadAsync(imageStream);
            
            // Vérifier les dimensions
            if (image.Width > _settings.MaxWidth || image.Height > _settings.MaxHeight)
            {
                return new ImageValidationResult
                {
                    IsValid = false,
                    ErrorMessage = $"Dimensions trop importantes ({image.Width}x{image.Height}). Maximum autorisé: {_settings.MaxWidth}x{_settings.MaxHeight}"
                };
            }

            return new ImageValidationResult { IsValid = true };
        }
        catch (Exception ex)
        {
            return new ImageValidationResult
            {
                IsValid = false,
                ErrorMessage = $"Fichier image invalide: {ex.Message}"
            };
        }
        finally
        {
            imageStream.Position = 0;
        }
    }

    public async Task<Stream> OptimizeImageAsync(Stream imageStream, ImageOptimizationOptions? options = null)
    {
        options ??= new ImageOptimizationOptions();
        
        try
        {
            imageStream.Position = 0;
            using var image = await Image.LoadAsync(imageStream);
            
            // Redimensionner si nécessaire
            if (options.MaxWidth.HasValue || options.MaxHeight.HasValue)
            {
                var maxWidth = options.MaxWidth ?? image.Width;
                var maxHeight = options.MaxHeight ?? image.Height;
                
                if (image.Width > maxWidth || image.Height > maxHeight)
                {
                    image.Mutate(x => x.Resize(new ResizeOptions
                    {
                        Size = new SixLabors.ImageSharp.Size(maxWidth, maxHeight),
                        Mode = ResizeMode.Max
                    }));
                }
            }

            var outputStream = new MemoryStream();
            var encoder = new JpegEncoder
            {
                Quality = options.Quality
            };

            await image.SaveAsync(outputStream, encoder);
            outputStream.Position = 0;
            
            return outputStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'optimisation de l'image");
            imageStream.Position = 0;
            return imageStream;
        }
    }

    private string GeneratePublicId(string fileName, string folder)
    {
        var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        var sanitizedName = System.Text.RegularExpressions.Regex.Replace(nameWithoutExtension, @"[^a-zA-Z0-9_-]", "_");
        return $"{folder}/{sanitizedName}_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}";
    }

    private Transformation CreateCloudinaryTransformation(ImageTransformation transformation)
    {
        var cloudinaryTransform = new Transformation();

        if (transformation.Width.HasValue)
            cloudinaryTransform = cloudinaryTransform.Width(transformation.Width.Value);
        
        if (transformation.Height.HasValue)
            cloudinaryTransform = cloudinaryTransform.Height(transformation.Height.Value);
        
        if (!string.IsNullOrEmpty(transformation.Crop))
            cloudinaryTransform = cloudinaryTransform.Crop(transformation.Crop);
        
        if (!string.IsNullOrEmpty(transformation.Gravity))
            cloudinaryTransform = cloudinaryTransform.Gravity(transformation.Gravity);
        
        if (transformation.Quality.HasValue)
            cloudinaryTransform = cloudinaryTransform.Quality(transformation.Quality.Value);

        return cloudinaryTransform;
    }
}
