// Force rebuild timestamp: 2025-01-12 - Updated interface compatibility
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Application.Interfaces;
using NafaPlace.Catalog.Domain.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace NafaPlace.Catalog.Infrastructure.Services
{
    public class ProductImageService : IProductImageService
    {
        private readonly ICatalogDbContext _context;
        private readonly ICloudinaryService _cloudinaryService;
        private readonly ILogger<ProductImageService> _logger;

        public ProductImageService(
            ICatalogDbContext context,
            ICloudinaryService cloudinaryService,
            ILogger<ProductImageService> logger)
        {
            _context = context;
            _cloudinaryService = cloudinaryService;
            _logger = logger;
        }

        public async Task<string> UploadImageAsync(string base64Image)
        {
            try
            {
                if (string.IsNullOrEmpty(base64Image))
                {
                    return string.Empty;
                }

                // Supprimer le préfixe data:image si présent
                string cleanBase64 = base64Image;
                if (base64Image.Contains(","))
                {
                    cleanBase64 = base64Image.Split(',')[1];
                }

                // Convertir en stream
                var imageBytes = Convert.FromBase64String(cleanBase64);
                using var imageStream = new MemoryStream(imageBytes);

                // Générer un nom de fichier unique
                var fileName = $"product_{Guid.NewGuid()}.jpg";

                // Upload vers Cloudinary
                var imageUrl = await _cloudinaryService.UploadImageAsync(imageStream, fileName, "products");

                return imageUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'upload de l'image produit");
                throw;
            }
        }

        public async Task<ProductImageDto> AddProductImageAsync(int productId, CreateProductImageRequest request)
        {
            try
            {
                // Upload de l'image
                var imageUrl = await UploadImageAsync(request.Image);

                // Créer l'entité ProductImage
                var productImage = new ProductImage
                {
                    ProductId = productId,
                    ImageUrl = imageUrl,
                    ThumbnailUrl = imageUrl, // Pour l'instant, même URL
                    IsMain = request.IsMain,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.ProductImages.Add(productImage);
                await _context.SaveChangesAsync();

                return new ProductImageDto
                {
                    Id = productImage.Id,
                    ProductId = productImage.ProductId,
                    Url = productImage.ImageUrl,
                    ThumbnailUrl = productImage.ThumbnailUrl,
                    IsMain = productImage.IsMain
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de l'ajout de l'image produit");
                throw;
            }
        }

        public async Task<IEnumerable<ProductImageDto>> AddBulkProductImagesAsync(int productId, IEnumerable<CreateProductImageRequest> requests)
        {
            var results = new List<ProductImageDto>();

            foreach (var request in requests)
            {
                var result = await AddProductImageAsync(productId, request);
                results.Add(result);
            }

            return results;
        }

        public async Task DeleteProductImageAsync(int imageId)
        {
            try
            {
                var image = await _context.ProductImages.FindAsync(imageId);
                if (image != null)
                {
                    _context.ProductImages.Remove(image);
                    await _context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la suppression de l'image {imageId}");
                throw;
            }
        }

        public async Task SetMainImageAsync(int productId, int imageId)
        {
            try
            {
                // Désactiver toutes les images principales du produit
                var productImages = await _context.ProductImages
                    .Where(pi => pi.ProductId == productId)
                    .ToListAsync();

                foreach (var img in productImages)
                {
                    img.IsMain = img.Id == imageId;
                    img.UpdatedAt = DateTime.UtcNow;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la définition de l'image principale {imageId}");
                throw;
            }
        }

        public async Task<bool> ValidateImageAsync(string image)
        {
            try
            {
                if (string.IsNullOrEmpty(image))
                    return false;

                // Validation basique du format base64
                if (image.Contains(","))
                {
                    var base64Data = image.Split(',')[1];
                    var imageBytes = Convert.FromBase64String(base64Data);
                    return imageBytes.Length > 0;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task<ProductImageDto?> GetProductImageAsync(int imageId)
        {
            try
            {
                var image = await _context.ProductImages.FindAsync(imageId);
                if (image == null)
                    return null;

                return new ProductImageDto
                {
                    Id = image.Id,
                    ProductId = image.ProductId,
                    Url = image.ImageUrl,
                    ThumbnailUrl = image.ThumbnailUrl,
                    IsMain = image.IsMain
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Erreur lors de la récupération de l'image {imageId}");
                throw;
            }
        }
    }
}
